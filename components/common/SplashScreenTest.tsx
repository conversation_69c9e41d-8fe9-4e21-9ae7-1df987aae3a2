"use client"

import React from "react"
import { useSplashStore } from "@/stores/themeStore"
import { Button } from "@/components/ui/button"

export function SplashScreenTest() {
  const { isVisible, isAnimating, showSplash, hideSplash } = useSplashStore()

  return (
    <div className="fixed bottom-4 right-4 z-40 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg border">
      <h3 className="text-sm font-semibold mb-2">Splash Screen Test</h3>
      <div className="space-y-2 text-xs">
        <p>Visible: {isVisible ? "Yes" : "No"}</p>
        <p>Animating: {isAnimating ? "Yes" : "No"}</p>
      </div>
      <div className="flex gap-2 mt-3">
        <Button
          size="sm"
          onClick={showSplash}
          disabled={isVisible}
          className="text-xs"
        >
          Show Splash
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={hideSplash}
          disabled={!isVisible}
          className="text-xs"
        >
          Hide Splash
        </Button>
      </div>
    </div>
  )
}
