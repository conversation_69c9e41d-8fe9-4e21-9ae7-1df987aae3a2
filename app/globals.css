@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Font Imports */
@font-face {
  font-family: "Legquinne-Regular";
  src: url("/fonts/Legquinne-Regular.otf") format("opentype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Legquinne-light";
  src: url("/fonts/Legquinne-light.otf") format("opentype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}


@font-face {
  font-family: "Sukar-Black-Font";
  src: url("/fonts/Sukar Black Font.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "Sukar-Regular";
  src: url("/fonts/Sukar Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Sukar-Bold";
  src: url("/fonts/Sukar Bold.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* Base reset and styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: "Legquinne-light", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  font-feature-settings: normal;
  font-variation-settings: normal;
}

/* Arabic font for RTL content */
html[dir="rtl"] {
  font-family: "Sukar-Regular", "Legquinne-light", system-ui, -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans",
    sans-serif;
}

body {
  margin: 0;
  line-height: inherit;
  max-width: 100vw;
  overflow-x: hidden;
  @apply bg-white dark:bg-secondary-600 text-gray-900 dark:text-slate-100;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Remove default button styles */
button {
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
  background-color: transparent;
  background-image: none;
  border: 0;
  cursor: pointer;
}

/* Remove default link styles */
a {
  color: inherit;
  text-decoration: inherit;
}

/* Custom scrollbar for dark mode */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-secondary-700;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-secondary-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-secondary-500;
}

/* Focus styles */
:focus-visible {
  @apply outline-2 outline-blue-500 dark:outline-blue-400 outline-offset-2;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.container {
  max-width: 88%;
  margin: 0 auto;
}

/* Font utility classes for language-specific fonts */
.font-en {
  font-family: "Legquinne-Regular", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

.font-ar {
  font-family: "Sukar Black Font", "Legquinne-Regular", system-ui, -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans",
    sans-serif;
}

/* Marquee Animation for Promotional Banner */
:root {
  --marquee-speed: 15s; /* Default speed - adjust this value to control speed */
}

@keyframes marquee {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes marquee-rtl {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(0%);
  }
}

.marquee {
  animation: marquee var(--marquee-speed) linear infinite;
}

.marquee-rtl {
  animation: marquee-rtl var(--marquee-speed) linear infinite;
}

/* Speed control classes */
.marquee-slow {
  --marquee-speed: 60s;
}

.marquee-normal {
  --marquee-speed: 15s;
}

.marquee-fast {
  --marquee-speed: 8s;
}

.marquee-very-fast {
  --marquee-speed: 5s;
}

/* Pause animation on hover */
.marquee-container:hover .marquee,
.marquee-container:hover .marquee-rtl {
  animation-play-state: paused;
}

.sukar {
  font-family: "Sukar-Regular", "Legquinne-Regular", system-ui, -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans",
    sans-serif;
  font-weight: 300;
}

/* Splash Screen Animations */
@keyframes splash-fade-out {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(1.1);
  }
}

@keyframes splash-logo-fade {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes splash-ring-pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.3;
  }
}

.splash-logo-animate {
  animation: splash-logo-fade 1.5s ease-out;
}

.splash-ring {
  animation: splash-ring-pulse 3s ease-in-out infinite;
}

.splash-fade-out {
  animation: splash-fade-out 1s ease-in-out forwards;
}

/* Animation delay utilities for splash screen */
.splash-delay-150 {
  animation-delay: 150ms;
}

.splash-delay-300 {
  animation-delay: 300ms;
}

.splash-delay-500 {
  animation-delay: 500ms;
}

.splash-delay-1000 {
  animation-delay: 1s;
}

.font-body {
  font-family: "Sukar-Regular", "Legquinne-Regular", system-ui, -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans",
    sans-serif;
}
.font-title {
  font-family: "Legquinne-light", "Sukar Regular", system-ui, -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans",
    sans-serif;
}
